# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Quick Start Commands

```bash
pnpm dev                 # Start Next.js development server
pnpm build              # Build the application  
pnpm start              # Start production server
pnpm lint               # Run ESLint
```

## Project Architecture

This is a **single Next.js application** (not a monorepo) that implements **BuddyBench** - a drag-and-drop tier list maker with local storage persistence.

### Tech Stack
- **Framework**: Next.js 15.2.4 with App Router
- **UI Components**: shadcn/ui built on Radix UI primitives
- **Styling**: Tailwind CSS with CSS variables theming
- **Drag & Drop**: @dnd-kit/core with sortable functionality
- **State Management**: React useState + custom useLocalStorage hook
- **Icons**: Lucide React
- **Typography**: Geist Sans & Mono fonts
- **Package Manager**: pnpm (workspace configured)

### Key Architecture Patterns

**Component Structure:**
- `app/` - Next.js App Router pages and layout
- `components/` - Reusable UI components
  - `ui/` - shadcn/ui primitive components
  - `buddy-bench.tsx` - Main application component
- `hooks/` - Custom React hooks (useLocalStorage)
- `lib/` - Utility functions (Tailwind cn helper)

**Data Flow:**
- All tier list data persists to localStorage using versioned keys (`buddybench-tiers-v2`, `buddybench-library-v2`)
- Main state managed in `BuddyBench` component with complex drag-and-drop logic
- Three drag scenarios handled: library-to-tier, tier-to-tier, and tier-to-trash

**Key Features Implemented:**
- SVG import/paste functionality for custom tier list items
- Drag-and-drop tier list creation with sortable items
- Screenshot generation with html2canvas
- Local storage persistence
- Color-coded tier system with customizable titles

### Configuration Notes

**Next.js Config:**
- ESLint and TypeScript errors ignored during builds (development focus)
- Image optimization disabled (unoptimized: true)

**TypeScript:**
- Strict mode enabled
- Path aliases: `@/*` maps to project root
- ES6 target with bundler module resolution

**Tailwind:**
- CSS variables for theming (HSL color system)
- Custom animations for accordion components
- Container centering with responsive breakpoints

## Development Workflow

The application is designed for rapid prototyping with build error checking disabled. When making changes:

1. **Component Development**: Follow shadcn/ui patterns - components use `cn()` utility for conditional styling
2. **State Management**: Use the existing useLocalStorage pattern for persistence
3. **Drag & Drop**: Extend the existing @dnd-kit implementation in buddy-bench.tsx
4. **Styling**: Use Tailwind with the established CSS variable theming system

## Important Implementation Details

- **SVG Handling**: App accepts SVG imports and clipboard paste events globally
- **Screenshot Feature**: Uses html2canvas with custom canvas composition for branded outputs
- **Data Versioning**: LocalStorage keys include version suffixes for data migration
- **Responsive Design**: Grid layouts use auto-fill with minimum item widths
- **No Backend**: Entirely client-side application with localStorage persistence