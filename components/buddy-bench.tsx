"use client"

/**
 * Buddy<PERSON><PERSON>ch - Interactive Tier List Creator
 *
 * A comprehensive React component for creating, managing, and sharing tier lists.
 * Features include:
 * - Drag & drop functionality for organizing items into tiers
 * - Support for both SVG and image content
 * - Image cropping capabilities
 * - Local storage persistence with data migration
 * - Export/import functionality (JSON/ZIP formats)
 * - Screenshot generation with custom titles
 * - Clipboard paste support for SVGs and images
 *
 * Architecture:
 * - Main component: BuddyBench (manages all state and logic)
 * - Sub-components: ItemDisplay, TierItemCard, LibraryItemCard, TierDropZone, TrashCan
 * - Drag & drop powered by @dnd-kit with three scenarios:
 *   1. Library to tier placement
 *   2. Tier to tier movement/sorting
 *   3. Item to trash removal
 *
 * State Management:
 * - Uses custom useLocalStorage hook for persistence
 * - Implements data migration for backward compatibility
 * - Comprehensive console logging for debugging
 */

import type React from "react"
import { useState, useMemo, useRef, useEffect, useCallback } from "react"
import {
  DndContext,
  type DragEndEvent,
  DragOverlay,
  type DragStartEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core"
import { arrayMove, sortableKeyboardCoordinates } from "@dnd-kit/sortable"
import { v4 as uuidv4 } from "uuid"
import html2canvas from "html2canvas"
import { type Crop, centerCrop, makeAspectCrop } from 'react-image-crop'
import 'react-image-crop/dist/ReactCrop.css'
import JSZip from 'jszip'
import { saveAs } from 'file-saver'

import useLocalStorage from "@/hooks/use-local-storage"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { TooltipProvider } from "@/components/ui/tooltip"
import { DialogTrigger } from "@/components/ui/dialog"
import { Camera } from "lucide-react"

// Import our extracted components
import LibrarySection from "./library-section"
import TierListSection from "./tier-list-section"
import { ScreenshotModal, ColorPickerModal, CropModal } from "./buddy-bench-modals"

// --- TYPE DEFINITIONS ---
interface LibraryItem {
  id: string
  content: string
  contentType: 'svg' | 'image'
  title: string
  dateAdded: string
  originalContent?: string // For storing original before cropping
}

type SortOption = 'name-asc' | 'name-desc' | 'date-asc' | 'date-desc' | 'type' | 'manual'

interface ExportData {
  version: string
  exportDate: string
  appVersion: string
  tiers: Tier[]
  library: LibraryItem[]
}

interface PlacedItem {
  instanceId: string
  libraryId: string
}

interface Tier {
  id: string
  title: string
  color: string
  items: PlacedItem[]
}

interface ActiveDragItem {
  type: "library" | "placed"
  libraryItem: LibraryItem
  instanceId?: string
}

// --- CONSTANTS ---
const defaultTiers: Tier[] = [
  { id: "S", title: "S", color: "#ef4444", items: [] },
  { id: "A", title: "A", color: "#f97316", items: [] },
  { id: "B", title: "B", color: "#f59e0b", items: [] },
]
const presetColors = [
  "#ef4444", // red-500
  "#f97316", // orange-500
  "#f59e0b", // amber-500
  "#eab308", // yellow-500
  "#84cc16", // lime-500
  "#22c55e", // green-500
  "#10b981", // emerald-500
  "#14b8a6", // teal-500
  "#06b6d4", // cyan-500
  "#0ea5e9", // sky-500
  "#3b82f6", // blue-500
  "#6366f1", // indigo-500
  "#8b5cf6", // violet-500
  "#a855f7", // purple-500
  "#d946ef", // fuchsia-500
  "#ec4899", // pink-500
  "#f43f5e", // rose-500
]

// Component definitions moved to separate files:
// - LibrarySection: ./library-section.tsx
// - TierListSection: ./tier-list-section.tsx
// - Modals: ./buddy-bench-modals.tsx

// --- DATA MIGRATION HELPERS ---
const migrateTierColors = (tiers: any[]): Tier[] => {
  const colorMap: Record<string, string> = {
    "bg-red-500": "#ef4444",
    "bg-orange-500": "#f97316",
    "bg-amber-500": "#f59e0b",
    "bg-yellow-500": "#eab308",
    "bg-lime-500": "#84cc16",
    "bg-green-500": "#22c55e",
    "bg-emerald-500": "#10b981",
    "bg-teal-500": "#14b8a6",
    "bg-cyan-500": "#06b6d4",
    "bg-sky-500": "#0ea5e9",
    "bg-blue-500": "#3b82f6",
    "bg-indigo-500": "#6366f1",
    "bg-violet-500": "#8b5cf6",
    "bg-purple-500": "#a855f7",
    "bg-fuchsia-500": "#d946ef",
    "bg-pink-500": "#ec4899",
    "bg-rose-500": "#f43f5e",
  }
  
  return tiers.map(tier => ({
    ...tier,
    color: colorMap[tier.color] || tier.color
  }))
}

const migrateLibraryItems = (items: any[]): LibraryItem[] => {
  return items.map(item => {
    // Handle old format with svgContent
    if ('svgContent' in item) {
      return {
        id: item.id,
        content: item.svgContent,
        contentType: 'svg' as const,
        title: item.title,
        dateAdded: item.dateAdded || new Date().toISOString()
      }
    }
    // Handle items without dateAdded
    if (!item.dateAdded) {
      return {
        ...item,
        dateAdded: new Date().toISOString()
      }
    }
    return item
  })
}

// --- MAIN COMPONENT ---
export default function BuddyBench() {
  console.log('🚀 BuddyBench component initializing...')

  const [rawTiers, setRawTiers] = useLocalStorage<any[]>("buddybench-tiers-v2", defaultTiers)
  const [rawLibrary, setRawLibrary] = useLocalStorage<any[]>("buddybench-library-v2", [])
  const [isClient, setIsClient] = useState(false)
  
  // Ensure client-side hydration matches server-side rendering
  useEffect(() => {
    setIsClient(true)
  }, [])
  
  // Migrate data on client side only
  const tiers = isClient ? migrateTierColors(rawTiers) : defaultTiers
  const library = isClient ? migrateLibraryItems(rawLibrary) : []
  
  const setTiers = (newTiers: Tier[] | ((prev: Tier[]) => Tier[])) => {
    if (typeof newTiers === 'function') {
      setRawTiers(prev => newTiers(migrateTierColors(prev)))
    } else {
      setRawTiers(newTiers)
    }
  }
  
  const setLibrary = (newLibrary: LibraryItem[] | ((prev: LibraryItem[]) => LibraryItem[])) => {
    if (typeof newLibrary === 'function') {
      setRawLibrary(prev => newLibrary(migrateLibraryItems(prev)))
    } else {
      setRawLibrary(newLibrary)
    }
  }
  const [activeDragItem, setActiveDragItem] = useState<ActiveDragItem | null>(null)
  const [isScreenshotModalOpen, setIsScreenshotModalOpen] = useState(false)
  const [screenshotTitle, setScreenshotTitle] = useState("My BuddyBench Tier List")
  const [screenshotParagraph, setScreenshotParagraph] = useState("Here's my definitive ranking!")
  const [isGenerating, setIsGenerating] = useState(false)
  const [isScreenshotting, setIsScreenshotting] = useState(false)
  const [isColorPickerOpen, setIsColorPickerOpen] = useState(false)
  const [selectedTierId, setSelectedTierId] = useState<string>("")
  const [customColor, setCustomColor] = useState("#ef4444")
  const [sortBy, setSortBy] = useState<SortOption>('date-desc')
  const [isCropModalOpen, setIsCropModalOpen] = useState(false)
  const [cropItem, setCropItem] = useState<LibraryItem | null>(null)
  const [crop, setCrop] = useState<Crop>()
  const [completedCrop, setCompletedCrop] = useState<Crop>()
  const [scale, setScale] = useState(1)
  const [rotate, setRotate] = useState(0)

  const fileInputRef = useRef<HTMLInputElement>(null)
  const importInputRef = useRef<HTMLInputElement>(null)
  const tierListRef = useRef<HTMLDivElement>(null)
  const imgRef = useRef<HTMLImageElement>(null)

  const libraryItemsById = useMemo(
    () => library.reduce((acc, item) => ({ ...acc, [item.id]: item }), {} as Record<string, LibraryItem>),
    [library],
  )

  const sortedLibrary = useMemo(() => {
    console.log('🔄 Sorting library with method:', sortBy)
    const sorted = [...library]
    switch (sortBy) {
      case 'name-asc':
        return sorted.sort((a, b) => a.title.localeCompare(b.title))
      case 'name-desc':
        return sorted.sort((a, b) => b.title.localeCompare(a.title))
      case 'date-asc':
        return sorted.sort((a, b) => new Date(a.dateAdded).getTime() - new Date(b.dateAdded).getTime())
      case 'date-desc':
        return sorted.sort((a, b) => new Date(b.dateAdded).getTime() - new Date(a.dateAdded).getTime())
      case 'type':
        return sorted.sort((a, b) => {
          if (a.contentType === b.contentType) return a.title.localeCompare(b.title)
          return a.contentType === 'svg' ? -1 : 1
        })
      case 'manual':
      default:
        return sorted // Keep original order for manual sorting
    }
  }, [library, sortBy])
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, { coordinateGetter: sortableKeyboardCoordinates }),
  )

  const wrapText = (
    ctx: CanvasRenderingContext2D,
    text: string,
    x: number,
    y: number,
    maxWidth: number,
    lineHeight: number,
  ): number => {
    const words = text.split(" ")
    let line = ""
    let testLine = ""
    let testWidth = 0
    let finalY = y
    let lineCount = 0

    for (let n = 0; n < words.length; n++) {
      testLine = line + words[n] + " "
      testWidth = ctx.measureText(testLine).width
      if (testWidth > maxWidth && n > 0) {
        ctx.fillText(line, x, finalY)
        line = words[n] + " "
        finalY += lineHeight
        lineCount++
      } else {
        line = testLine
      }
    }
    ctx.fillText(line, x, finalY)
    lineCount++
    return finalY + lineHeight * (lineCount - 1)
  }

  useEffect(() => {
    const handlePaste = (event: ClipboardEvent) => {
      console.log('📋 Paste event detected')
      const activeEl = document.activeElement
      if (activeEl && (activeEl.tagName === "INPUT" || activeEl.tagName === "TEXTAREA")) {
        console.log('❌ Paste ignored - active element is input/textarea')
        return
      }

      // Handle text content (SVG code)
      const text = event.clipboardData?.getData("text/plain")
      if (text) {
        console.log('📝 Text content detected in clipboard:', text.length, 'characters')
        const trimmedText = text.trim()
        if (trimmedText.startsWith("<svg") && trimmedText.endsWith("</svg>")) {
          console.log('🎨 SVG content detected, prompting for title...')
          event.preventDefault()
          const title = prompt("Enter a title for the pasted SVG:", "Pasted SVG")
          if (title) {
            const newItem: LibraryItem = {
              id: uuidv4(),
              content: trimmedText,
              contentType: 'svg',
              title,
              dateAdded: new Date().toISOString()
            }
            console.log('✅ SVG item created:', newItem.id)
            setLibrary((prev) => [...prev, newItem])
            alert("SVG pasted successfully from clipboard!")
          } else {
            console.log('❌ User cancelled SVG paste')
          }
          return
        }
      }
      
      // Handle image content
      const items = event.clipboardData?.items
      if (items) {
        console.log('📎 Checking clipboard items:', items.length)
        for (let i = 0; i < items.length; i++) {
          const item = items[i]
          console.log('📎 Clipboard item:', item.type)
          if (item.type.startsWith('image/')) {
            console.log('🖼️ Image content detected:', item.type)
            event.preventDefault()
            const file = item.getAsFile()
            if (file) {
              console.log('📁 Image file extracted:', file.name, file.size, 'bytes')
              const reader = new FileReader()
              reader.onload = (e) => {
                const title = prompt("Enter a title for the pasted image:", "Pasted Image")
                if (title) {
                  const newItem: LibraryItem = {
                    id: uuidv4(),
                    content: e.target?.result as string,
                    contentType: 'image',
                    title,
                    dateAdded: new Date().toISOString()
                  }
                  console.log('✅ Image item created:', newItem.id)
                  setLibrary((prev) => [...prev, newItem])
                  alert("Image pasted successfully from clipboard!")
                } else {
                  console.log('❌ User cancelled image paste')
                }
              }
              reader.readAsDataURL(file)
            }
            break
          }
        }
      }
    }
    document.addEventListener("paste", handlePaste)
    return () => document.removeEventListener("paste", handlePaste)
  }, [setLibrary])

  const handleRenameLibraryItem = useCallback((itemId: string, newTitle: string) => {
    console.log('✏️ Renaming library item:', itemId, 'to:', newTitle)
    setLibrary((prev) => prev.map((item) => (item.id === itemId ? { ...item, title: newTitle } : item)))
  }, [setLibrary])

  const handleDuplicateLibraryItem = useCallback((itemId: string) => {
    console.log('📋 Duplicating library item:', itemId)
    const itemToDuplicate = library.find((item) => item.id === itemId)
    if (!itemToDuplicate) {
      console.warn('❌ Item to duplicate not found:', itemId)
      return
    }
    const newItem: LibraryItem = {
      ...itemToDuplicate,
      id: uuidv4(),
      title: `${itemToDuplicate.title} (Copy)`,
      dateAdded: new Date().toISOString()
    }
    console.log('✅ Created duplicate item:', newItem.id)
    setLibrary((prev) => [...prev, newItem])
  }, [library, setLibrary])

  const handleRemoveLibraryItem = (itemId: string) => {
    console.log('🗑️ Attempting to remove library item:', itemId)
    if (window.confirm("Remove this item from the library and all tiers? This cannot be undone.")) {
      console.log('✅ User confirmed removal, proceeding...')
      setLibrary((prev) => prev.filter((item) => item.id !== itemId))
      setTiers((prev) =>
        prev.map((tier) => ({ ...tier, items: tier.items.filter((item) => item.libraryId !== itemId) })),
      )
      console.log('✅ Item removed from library and all tiers')
    } else {
      console.log('❌ User cancelled removal')
    }
  }

  const addTier = () =>
    setTiers([
      ...tiers,
      { id: uuidv4(), title: "New Tier", color: presetColors[Math.floor(Math.random() * presetColors.length)], items: [] },
    ])
  const removeTier = (tierId: string) => {
    if (window.confirm("Are you sure?")) setTiers(tiers.filter((t) => t.id !== tierId))
  }
  const updateTierTitle = (tierId: string, newTitle: string) =>
    setTiers(tiers.map((t) => (t.id === tierId ? { ...t, title: newTitle } : t)))
  const openColorPicker = (tierId: string) => {
    const currentTier = tiers.find((t) => t.id === tierId)
    if (currentTier) {
      setSelectedTierId(tierId)
      setCustomColor(currentTier.color)
      setIsColorPickerOpen(true)
    }
  }

  const updateTierColor = (tierId: string, newColor: string) => {
    setTiers(tiers.map((t) => (t.id === tierId ? { ...t, color: newColor } : t)))
  }

  const handleColorChange = () => {
    if (selectedTierId && customColor) {
      updateTierColor(selectedTierId, customColor)
      setIsColorPickerOpen(false)
    }
  }

  const openCropModal = (item: LibraryItem) => {
    console.log('✂️ Opening crop modal for item:', item.id, item.contentType)
    if (item.contentType === 'image') {
      setCropItem(item)
      setCrop(undefined)
      setScale(1)
      setRotate(0)
      setIsCropModalOpen(true)
      console.log('✅ Crop modal opened successfully')
    } else {
      console.warn('❌ Cannot crop non-image item:', item.contentType)
    }
  }

  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget
    setCrop(centerCrop(
      makeAspectCrop(
        {
          unit: '%',
          width: 90,
        },
        1,
        width,
        height,
      ),
      width,
      height,
    ))
  }

  const getCroppedImg = (image: HTMLImageElement, crop: Crop): Promise<string> => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    if (!ctx) {
      throw new Error('No 2d context')
    }

    const scaleX = image.naturalWidth / image.width
    const scaleY = image.naturalHeight / image.height
    const pixelRatio = window.devicePixelRatio

    canvas.width = crop.width * scaleX * pixelRatio
    canvas.height = crop.height * scaleY * pixelRatio

    ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0)
    ctx.imageSmoothingQuality = 'high'

    ctx.drawImage(
      image,
      crop.x * scaleX,
      crop.y * scaleY,
      crop.width * scaleX,
      crop.height * scaleY,
      0,
      0,
      crop.width * scaleX,
      crop.height * scaleY,
    )

    return new Promise((resolve) => {
      canvas.toBlob((blob) => {
        if (!blob) {
          throw new Error('Canvas is empty')
        }
        const reader = new FileReader()
        reader.onload = () => resolve(reader.result as string)
        reader.readAsDataURL(blob)
      }, 'image/png')
    })
  }

  const applyCrop = async () => {
    console.log('✂️ Applying crop to item:', cropItem?.id)
    if (!imgRef.current || !completedCrop || !cropItem) {
      console.warn('❌ Missing required crop data:', {
        hasImgRef: !!imgRef.current,
        hasCompletedCrop: !!completedCrop,
        hasCropItem: !!cropItem
      })
      return
    }

    try {
      console.log('🔄 Processing crop with dimensions:', completedCrop)
      const croppedImageUrl = await getCroppedImg(imgRef.current, completedCrop)

      const updatedItem = {
        ...cropItem,
        content: croppedImageUrl,
        originalContent: cropItem.originalContent || cropItem.content
      }

      console.log('✅ Crop applied successfully, updating library')
      setLibrary(prev => prev.map(item =>
        item.id === cropItem.id ? updatedItem : item
      ))

      setIsCropModalOpen(false)
      setCropItem(null)
      console.log('✅ Crop modal closed')
    } catch (error) {
      console.error('❌ Error cropping image:', error)
      alert('Failed to crop image. Please try again.')
    }
  }

  // Export system with JSON/ZIP format detection
  const handleExportTierList = async () => {
    console.log('📤 Starting tier list export...')
    const hasImages = library.some(item => item.contentType === 'image')
    console.log('📊 Export stats:', {
      totalItems: library.length,
      hasImages,
      totalTiers: tiers.length
    })

    const exportData: ExportData = {
      version: "2.0",
      exportDate: new Date().toISOString(),
      appVersion: "BuddyBench v2.0",
      tiers,
      library
    }

    if (hasImages) {
      // Create ZIP file for mixed content
      const zip = new JSZip()
      
      // Add JSON metadata
      zip.file("buddybench-data.json", JSON.stringify(exportData, null, 2))
      
      // Add image files
      const imageFolder = zip.folder("images")
      const libraryWithImagePaths: LibraryItem[] = []
      
      for (const item of library) {
        if (item.contentType === 'image') {
          // Extract image data from data URL
          const base64Data = item.content.split(',')[1]
          const mimeType = item.content.split(';')[0].split(':')[1]
          const extension = mimeType.split('/')[1]
          const filename = `${item.id}.${extension}`
          
          imageFolder?.file(filename, base64Data, { base64: true })
          
          // Store reference to image file
          libraryWithImagePaths.push({
            ...item,
            content: `./images/${filename}`, // Replace data URL with file path
          })
        } else {
          // Keep SVGs as inline content
          libraryWithImagePaths.push(item)
        }
      }
      
      // Update export data with file references
      const updatedExportData = {
        ...exportData,
        library: libraryWithImagePaths
      }
      
      // Update the JSON file with file references
      zip.file("buddybench-data.json", JSON.stringify(updatedExportData, null, 2))
      
      // Generate and download ZIP
      const content = await zip.generateAsync({ type: "blob" })
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-')
      saveAs(content, `buddybench-export-${timestamp}.zip`)
      
    } else {
      // Create JSON file for SVG-only content
      const jsonString = JSON.stringify(exportData, null, 2)
      const blob = new Blob([jsonString], { type: 'application/json' })
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-')
      saveAs(blob, `buddybench-export-${timestamp}.json`)
    }
  }

  const handleImportTierList = async (file: File) => {
    console.log('📥 Starting tier list import:', file.name, file.size, 'bytes')
    try {
      if (file.name.endsWith('.json')) {
        console.log('📄 Processing JSON import...')
        // Handle JSON import
        const text = await file.text()
        const importData: ExportData = JSON.parse(text)

        // Validate import data
        if (!importData.tiers || !importData.library) {
          throw new Error('Invalid BuddyBench export file')
        }

        console.log('✅ JSON import validation passed:', {
          version: importData.version,
          itemCount: importData.library.length,
          tierCount: importData.tiers.length
        })

        // Import data
        setTiers(importData.tiers)
        setLibrary(importData.library)
        alert(`Successfully imported ${importData.library.length} items and ${importData.tiers.length} tiers from JSON export`)
        
      } else if (file.name.endsWith('.zip')) {
        // Handle ZIP import
        const zip = new JSZip()
        const zipContent = await zip.loadAsync(file)
        
        // Extract JSON metadata
        const jsonFile = zipContent.file('buddybench-data.json')
        if (!jsonFile) {
          throw new Error('No buddybench-data.json found in ZIP file')
        }
        
        const jsonText = await jsonFile.async('text')
        const importData: ExportData = JSON.parse(jsonText)
        
        // Process library items and restore images
        const restoredLibrary: LibraryItem[] = []
        
        for (const item of importData.library) {
          if (item.contentType === 'image' && item.content.startsWith('./images/')) {
            // Restore image from ZIP file
            const imagePath = item.content.replace('./', '')
            const imageFile = zipContent.file(imagePath)
            
            if (imageFile) {
              const imageBlob = await imageFile.async('blob')
              const reader = new FileReader()
              
              const dataUrl = await new Promise<string>((resolve) => {
                reader.onload = (e) => resolve(e.target?.result as string)
                reader.readAsDataURL(imageBlob)
              })
              
              restoredLibrary.push({
                ...item,
                content: dataUrl
              })
            } else {
              console.warn(`Image file not found: ${imagePath}`)
              // Skip missing image files
            }
          } else {
            // Keep SVGs and other content as-is
            restoredLibrary.push(item)
          }
        }
        
        // Import data
        setTiers(importData.tiers)
        setLibrary(restoredLibrary)
        alert(`Successfully imported ${restoredLibrary.length} items and ${importData.tiers.length} tiers from ZIP export`)
        
      } else {
        throw new Error('Please select a .json or .zip BuddyBench export file')
      }
    } catch (error) {
      console.error('Import failed:', error)
      alert(`Import failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  const handleImportFile = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return
    
    const file = files[0]
    if (file.name.endsWith('.json') || file.name.endsWith('.zip')) {
      handleImportTierList(file)
    } else {
      alert('Please select a .json or .zip BuddyBench export file')
    }
    
    // Reset the input
    event.target.value = ''
  }

  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files) return
    const imageFiles = Array.from(files).filter((file) => file.type.startsWith("image/"))
    if (imageFiles.length === 0) {
      alert("Please select image files only.")
      return
    }
    const newItemsPromises = imageFiles.map(
      (file) =>
        new Promise<LibraryItem>((resolve, reject) => {
          const reader = new FileReader()
          reader.onload = (e) => {
            const title = prompt(`Enter a title for "${file.name}":`, file.name.replace(/\.[^.]+$/, ""))
            if (title) {
              const content = e.target?.result as string
              const contentType = file.type === "image/svg+xml" ? 'svg' : 'image'
              resolve({ 
                id: uuidv4(), 
                content, 
                contentType,
                title,
                dateAdded: new Date().toISOString()
              })
            } else {
              reject(new Error("Cancelled"))
            }
          }
          reader.onerror = reject
          if (file.type === "image/svg+xml") {
            reader.readAsText(file)
          } else {
            reader.readAsDataURL(file)
          }
        }),
    )
    Promise.all(newItemsPromises)
      .then((newItems) => setLibrary((p) => [...p, ...newItems]))
      .catch(console.error)
    if (fileInputRef.current) fileInputRef.current.value = ""
  }

  const handleGenerateScreenshot = async () => {
    console.log('📸 Starting screenshot generation...')
    if (!tierListRef.current) {
      console.error('❌ Tier list ref not found')
      return
    }

    setIsScreenshotting(true)
    setIsGenerating(true)
    console.log('🔄 Screenshot state set, waiting for DOM update...')

    setTimeout(async () => {
      try {
        console.log('📷 Capturing tier list with html2canvas...')
        const tierListCanvas = await html2canvas(tierListRef.current!, {
          backgroundColor: null,
          scale: 2,
          useCORS: true,
        })
        console.log('✅ Tier list captured:', tierListCanvas.width, 'x', tierListCanvas.height)
        const tempCtx = document.createElement("canvas").getContext("2d")
        if (!tempCtx) throw new Error("No context")
        const padding = 50,
          spacing = 25,
          titleSize = 48,
          pSize = 24,
          pLineHeight = 30
        tempCtx.font = `${pSize}px sans-serif`
        const finalY = wrapText(tempCtx, screenshotParagraph, 0, 0, tierListCanvas.width / 2 - padding * 2, pLineHeight)
        const textHeight = finalY + pLineHeight
        const totalWidth = tierListCanvas.width / 2 + padding * 2
        const totalHeight = padding + titleSize + spacing + textHeight + spacing + tierListCanvas.height / 2 + padding
        const finalCanvas = document.createElement("canvas")
        finalCanvas.width = totalWidth
        finalCanvas.height = totalHeight
        const ctx = finalCanvas.getContext("2d")
        if (!ctx) throw new Error("No context")
        ctx.fillStyle = "#111827"
        ctx.fillRect(0, 0, totalWidth, totalHeight)
        ctx.fillStyle = "#ffffff"
        ctx.font = `bold ${titleSize}px sans-serif`
        ctx.textAlign = "center"
        ctx.fillText(screenshotTitle, totalWidth / 2, padding + titleSize)
        ctx.font = `${pSize}px sans-serif`
        wrapText(
          ctx,
          screenshotParagraph,
          totalWidth / 2,
          padding + titleSize + spacing + pSize,
          totalWidth - padding * 2,
          pLineHeight,
        )
        ctx.drawImage(
          tierListCanvas,
          padding,
          padding + titleSize + spacing + textHeight + spacing,
          tierListCanvas.width / 2,
          tierListCanvas.height / 2,
        )
        const link = document.createElement("a")
        link.download = `${screenshotTitle.toLowerCase().replace(/\s/g, "-")}.png`
        link.href = finalCanvas.toDataURL("image/png")
        link.click()
      } catch (error) {
        console.error(error)
        alert("Failed to generate screenshot.")
      } finally {
        setIsGenerating(false)
        setIsScreenshotting(false)
        setIsScreenshotModalOpen(false)
      }
    }, 100)
  }

  const findTierForInstance = (instanceId: string) =>
    tiers.find((tier) => tier.items.some((item) => item.instanceId === instanceId))

  const handleDragStart = useCallback((event: DragStartEvent) => {
    console.log('🎯 Drag started:', event.active.id, event.active.data.current)
    const { active } = event
    const { type, libraryId, instanceId } = active.data.current || {}
    if (type && libraryId && libraryItemsById[libraryId]) {
      console.log('✅ Setting active drag item:', { type, libraryId, instanceId })
      setActiveDragItem({ type, libraryItem: libraryItemsById[libraryId], instanceId })
    } else {
      console.warn('❌ Invalid drag data:', { type, libraryId, instanceId })
    }
  }, [libraryItemsById])

  const handleLibraryReorder = useCallback((newOrder: LibraryItem[]) => {
    console.log('📚 Reordering library items:', newOrder.map(item => item.id))
    setLibrary(newOrder)
  }, [setLibrary])

  const handleDragEnd = (event: DragEndEvent) => {
    console.log('🎯 Drag ended:', {
      activeId: event.active.id,
      overId: event.over?.id,
      activeData: event.active.data.current,
      overData: event.over?.data.current
    })
    setActiveDragItem(null)
    const { active, over } = event

    if (!over) {
      console.log('❌ No drop target found')
      return
    }

    const activeId = active.id as string
    const overId = over.id as string

    // Handle library item reordering (manual sort)
    if (active.data.current?.type === "library-sort" && sortBy === 'manual') {
      console.log('📚 Handling library reorder')
      const oldIndex = sortedLibrary.findIndex(item => item.id === activeId)
      const newIndex = sortedLibrary.findIndex(item => item.id === overId)

      if (oldIndex !== -1 && newIndex !== -1 && oldIndex !== newIndex) {
        const reorderedLibrary = arrayMove(sortedLibrary, oldIndex, newIndex)
        handleLibraryReorder(reorderedLibrary)
        console.log('✅ Library reordered successfully')
      }
      return
    }

    // Scenario 0: Dropping a placed item in the trash
    if (over.id === "trash-can" && active.data.current?.type === "placed") {
      console.log('🗑️ Dropping item in trash:', activeId)
      const sourceTier = findTierForInstance(activeId)
      if (sourceTier) {
        console.log('✅ Removing item from tier:', sourceTier.id)
        setTiers((prevTiers) =>
          prevTiers.map((t) =>
            t.id === sourceTier.id ? { ...t, items: t.items.filter((i) => i.instanceId !== activeId) } : t,
          ),
        )
      }
      return
    }

    // Find the destination tier - handle both tier drop zones and sortable items
    let destTier: Tier | undefined
    let isDropOnItem = false

    // Check if dropping directly on a tier drop zone
    if (over.data.current?.type === "tier") {
      destTier = tiers.find((t) => t.id === over.data.current?.tierId)
      console.log('📍 Dropping on tier zone:', destTier?.id)
    }
    // Check if dropping on an item within a tier (sortable context)
    else if (over.data.current?.sortable?.containerId) {
      destTier = tiers.find((t) => t.id === over.data.current?.sortable?.containerId)
      isDropOnItem = true
      console.log('📍 Dropping on item in tier:', destTier?.id)
    }
    // Fallback: check if over.id matches a tier ID
    else {
      destTier = tiers.find((t) => t.id === overId)
      console.log('📍 Fallback: dropping on tier:', destTier?.id)
    }

    if (!destTier) {
      console.warn('❌ No destination tier found')
      return
    }

    // Scenario 1: Dragging from the library into a tier
    if (active.data.current?.type === "library") {
      const newPlacedItem: PlacedItem = { instanceId: uuidv4(), libraryId: activeId }
      
      let insertionIndex = destTier.items.length // Default to end

      // If dropping on an existing item, insert at that position
      if (isDropOnItem) {
        const overIndex = destTier.items.findIndex((i) => i.instanceId === overId)
        insertionIndex = overIndex !== -1 ? overIndex : destTier.items.length
      }

      setTiers((prevTiers) =>
        prevTiers.map((t) => {
          if (t.id === destTier.id) {
            const newItems = [...t.items]
            newItems.splice(insertionIndex, 0, newPlacedItem)
            return { ...t, items: newItems }
          }
          return t
        }),
      )
      return
    }

    // Scenario 2: Moving a placed item
    if (active.data.current?.type === "placed") {
      const sourceTier = findTierForInstance(activeId)
      if (!sourceTier) return

      // Sub-scenario 2a: Sorting within the same tier
      if (sourceTier.id === destTier.id && isDropOnItem) {
        if (activeId === overId) return // Dropped on itself
        const oldIndex = sourceTier.items.findIndex((i) => i.instanceId === activeId)
        const newIndex = destTier.items.findIndex((i) => i.instanceId === overId)
        if (oldIndex !== -1 && newIndex !== -1) {
          setTiers((prevTiers) =>
            prevTiers.map((t) =>
              t.id === sourceTier.id ? { ...t, items: arrayMove(t.items, oldIndex, newIndex) } : t,
            ),
          )
        }
      } else {
        // Sub-scenario 2b: Moving between different tiers OR dropping on tier zone
        const [movedItem] = sourceTier.items.filter((i) => i.instanceId === activeId)
        if (!movedItem) return

        let insertionIndex = destTier.items.length // Default to end

        // If dropping on an existing item, insert at that position
        if (isDropOnItem) {
          const overIndex = destTier.items.findIndex((i) => i.instanceId === overId)
          insertionIndex = overIndex !== -1 ? overIndex : destTier.items.length
        }

        setTiers((prevTiers) =>
          prevTiers.map((t) => {
            // Remove from source tier
            if (t.id === sourceTier.id) {
              return { ...t, items: t.items.filter((i) => i.instanceId !== activeId) }
            }
            // Add to destination tier
            if (t.id === destTier.id) {
              const newItems = [...t.items]
              newItems.splice(insertionIndex, 0, movedItem)
              return { ...t, items: newItems }
            }
            return t
          })
        )
      }
    }
  }

  return (
    <TooltipProvider>
      <style>{`.screenshot-mode .tier-controls, .screenshot-mode .item-title { display: none; } .screenshot-mode .tier-title-input { box-shadow: none !important; border-color: transparent !important; pointer-events: none; }`}</style>

      {/* Sticky Screenshot Button */}
      <div className="fixed top-4 right-4 z-50">
        <ScreenshotModal
          isOpen={isScreenshotModalOpen}
          onOpenChange={setIsScreenshotModalOpen}
          title={screenshotTitle}
          onTitleChange={setScreenshotTitle}
          paragraph={screenshotParagraph}
          onParagraphChange={setScreenshotParagraph}
          onGenerate={handleGenerateScreenshot}
          isGenerating={isGenerating}
        />
        <DialogTrigger asChild>
          <Button variant="secondary" className="shadow-lg">
            <Camera className="mr-2 h-4 w-4" /> Screenshot
          </Button>
        </DialogTrigger>
      </div>

      <div className={isScreenshotting ? "screenshot-mode" : ""}>
        <DndContext sensors={sensors} onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
          <div className="space-y-8">
            <LibrarySection
              library={sortedLibrary}
              sortBy={sortBy}
              onSortChange={setSortBy}
              onLibraryReorder={handleLibraryReorder}
              onRename={handleRenameLibraryItem}
              onDuplicate={handleDuplicateLibraryItem}
              onRemove={handleRemoveLibraryItem}
              onCrop={openCropModal}
              onImportImages={() => fileInputRef.current?.click()}
              onImportTierList={() => importInputRef.current?.click()}
              onExportTierList={handleExportTierList}
            />

            <Input
              ref={fileInputRef}
              type="file"
              className="hidden"
              accept="image/*"
              multiple
              onChange={handleFileImport}
            />
            <Input
              ref={importInputRef}
              type="file"
              className="hidden"
              accept=".json,.zip"
              onChange={handleImportFile}
            />

            <TierListSection
              tiers={tiers}
              libraryItemsById={libraryItemsById}
              onUpdateTierTitle={updateTierTitle}
              onOpenColorPicker={openColorPicker}
              onRemoveTier={removeTier}
              onAddTier={addTier}
              tierListRef={tierListRef}
            />
          </div>
          <DragOverlay>
            {activeDragItem ? (
              <div className="w-20 h-20 p-1 bg-muted rounded-md flex items-center justify-center scale-110 shadow-lg">
                {activeDragItem.libraryItem.contentType === 'svg' ? (
                  <div
                    className="w-full h-full flex items-center justify-center"
                    dangerouslySetInnerHTML={{ __html: activeDragItem.libraryItem.content }}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <img
                      src={activeDragItem.libraryItem.content}
                      alt={activeDragItem.libraryItem.title}
                      className="w-full h-full object-contain"
                    />
                  </div>
                )}
              </div>
            ) : null}
          </DragOverlay>
        </DndContext>
      </div>

      {/* Color Picker Modal */}
      <ColorPickerModal
        isOpen={isColorPickerOpen}
        onOpenChange={setIsColorPickerOpen}
        customColor={customColor}
        onColorChange={setCustomColor}
        onApply={handleColorChange}
        presetColors={presetColors}
      />

      {/* Crop Modal */}
      <CropModal
        isOpen={isCropModalOpen}
        onOpenChange={setIsCropModalOpen}
        cropItem={cropItem}
        crop={crop}
        onCropChange={setCrop}
        onCropComplete={setCompletedCrop}
        scale={scale}
        onScaleChange={setScale}
        rotate={rotate}
        onRotateChange={setRotate}
        onImageLoad={onImageLoad}
        onApplyCrop={applyCrop}
        completedCrop={completedCrop}
        imgRef={imgRef}
      />
    </TooltipProvider>
  )
}
