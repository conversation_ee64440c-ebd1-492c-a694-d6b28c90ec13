"use client"

import React, { useRef } from "react"
import { SortableContext, useSortable, rectSortingStrategy } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import { useDraggable } from "@dnd-kit/core"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip"
import { Upload, FolderOpen, Download, Copy, Pencil, CropIcon, Trash2 } from "lucide-react"

// Types
interface LibraryItem {
  id: string
  content: string
  contentType: 'svg' | 'image'
  title: string
  dateAdded: string
  originalContent?: string
}

type SortOption = 'manual' | 'name-asc' | 'name-desc' | 'date-asc' | 'date-desc' | 'type'

interface LibrarySectionProps {
  library: LibraryItem[]
  sortBy: SortOption
  onSortChange: (sort: SortOption) => void
  onLibraryReorder: (newOrder: LibraryItem[]) => void
  onRename: (id: string, newTitle: string) => void
  onDuplicate: (id: string) => void
  onRemove: (id: string) => void
  onCrop?: (item: LibraryItem) => void
  onImportImages: () => void
  onImportTierList: () => void
  onExportTierList: () => void
}

// Item Display Component
function ItemDisplay({ item }: { item: LibraryItem }) {
  console.log('🎨 Rendering item display:', item.id, item.contentType)
  
  if (item.contentType === 'svg') {
    return (
      <div 
        className="w-full h-full flex items-center justify-center" 
        dangerouslySetInnerHTML={{ __html: item.content }} 
      />
    )
  } else {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <img 
          src={item.content} 
          alt={item.title} 
          className="w-full h-full object-contain"
          onError={(e) => {
            console.error('❌ Failed to load image:', item.id, e)
          }}
          onLoad={() => {
            console.log('✅ Image loaded successfully:', item.id)
          }}
        />
      </div>
    )
  }
}

// Sortable Library Item Card
function SortableLibraryItemCard({
  item,
  onRename,
  onDuplicate,
  onRemove,
  onCrop,
  isManualSort
}: {
  item: LibraryItem
  onRename: (id: string, newTitle: string) => void
  onDuplicate: (id: string) => void
  onRemove: (id: string) => void
  onCrop?: (item: LibraryItem) => void
  isManualSort: boolean
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: item.id,
    data: { type: "library-sort", libraryId: item.id },
    disabled: !isManualSort
  })

  // Also support dragging to tiers
  const { attributes: dragAttributes, listeners: dragListeners, setNodeRef: dragNodeRef } = useDraggable({
    id: item.id,
    data: { type: "library", libraryId: item.id },
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  }

  const handleRename = (e: React.MouseEvent) => {
    e.stopPropagation()
    const newTitle = prompt("Enter new name for this icon:", item.title)
    if (newTitle && newTitle.trim() !== "") {
      onRename(item.id, newTitle.trim())
    }
  }

  // Combine refs
  const combinedRef = (node: HTMLElement | null) => {
    setNodeRef(node)
    dragNodeRef(node)
  }

  // Combine attributes and listeners
  const combinedAttributes = { ...attributes, ...dragAttributes }
  const combinedListeners = isManualSort ? { ...listeners, ...dragListeners } : dragListeners

  return (
    <div className="flex flex-col items-center gap-1" style={style}>
      <div className="relative group">
        <div 
          ref={combinedRef} 
          {...combinedAttributes} 
          {...combinedListeners} 
          className={`w-20 h-20 p-1 bg-muted rounded-md ${isManualSort ? 'cursor-grab active:cursor-grabbing' : 'cursor-grab'}`}
        >
          <ItemDisplay item={item} />
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-1/2 bg-gradient-to-t from-black/70 to-transparent flex items-end justify-center gap-1 p-1 opacity-0 group-hover:opacity-100 transition-opacity rounded-b-md">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="icon"
                variant="ghost"
                className="h-7 w-7 text-white hover:bg-white/20"
                onClick={handleRename}
              >
                <Pencil className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Rename</TooltipContent>
          </Tooltip>
          {item.contentType === 'image' && onCrop && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="icon"
                  variant="ghost"
                  className="h-7 w-7 text-white hover:bg-white/20"
                  onClick={() => onCrop(item)}
                >
                  <CropIcon className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Crop</TooltipContent>
            </Tooltip>
          )}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="icon"
                variant="ghost"
                className="h-7 w-7 text-white hover:bg-white/20"
                onClick={() => onDuplicate(item.id)}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Duplicate</TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="icon"
                variant="ghost"
                className="h-7 w-7 text-white hover:bg-white/20 hover:text-red-500"
                onClick={() => onRemove(item.id)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Remove</TooltipContent>
          </Tooltip>
        </div>
      </div>
      <p className="text-xs text-foreground truncate w-20 text-center">{item.title}</p>
    </div>
  )
}

// Main Library Section Component
export default function LibrarySection({
  library,
  sortBy,
  onSortChange,
  onLibraryReorder,
  onRename,
  onDuplicate,
  onRemove,
  onCrop,
  onImportImages,
  onImportTierList,
  onExportTierList
}: LibrarySectionProps) {
  console.log('📚 Rendering LibrarySection with', library.length, 'items, sort:', sortBy)

  const isManualSort = sortBy === 'manual'

  return (
    <Card>
      <CardContent className="p-4">
        <h2 className="text-2xl font-bold mb-4 text-center">Item Library</h2>
        <div className="flex flex-wrap justify-center gap-4 mb-4">
          <Button onClick={onImportImages}>
            <Upload className="mr-2 h-4 w-4" /> Import Images
          </Button>
          <Button onClick={onImportTierList}>
            <FolderOpen className="mr-2 h-4 w-4" /> Import Tier List
          </Button>
          <Button onClick={onExportTierList}>
            <Download className="mr-2 h-4 w-4" /> Export Tier List
          </Button>
          <Select value={sortBy} onValueChange={(value: SortOption) => onSortChange(value)}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Sort by..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="manual">Manual Order</SelectItem>
              <SelectItem value="date-desc">Newest First</SelectItem>
              <SelectItem value="date-asc">Oldest First</SelectItem>
              <SelectItem value="name-asc">Name A-Z</SelectItem>
              <SelectItem value="name-desc">Name Z-A</SelectItem>
              <SelectItem value="type">Type (SVG First)</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <p className="text-center text-sm text-muted-foreground mb-4">
          {isManualSort 
            ? "Drag items to reorder them manually, or drag to tiers to place them."
            : "Or paste SVG code or images anywhere on the page."
          }
        </p>
        
        {library.length > 0 ? (
          <SortableContext items={library.map(item => item.id)} strategy={rectSortingStrategy}>
            <div className="grid grid-cols-[repeat(auto-fill,minmax(80px,1fr))] gap-x-4 gap-y-6 p-2 bg-muted/50 rounded-lg min-h-[116px]">
              {library.map((item) => (
                <SortableLibraryItemCard
                  key={item.id}
                  item={item}
                  onRename={onRename}
                  onDuplicate={onDuplicate}
                  onRemove={onRemove}
                  onCrop={onCrop}
                  isManualSort={isManualSort}
                />
              ))}
            </div>
          </SortableContext>
        ) : (
          <p className="text-center text-muted-foreground">Import some images to get started!</p>
        )}
      </CardContent>
    </Card>
  )
}
