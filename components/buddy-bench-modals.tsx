"use client"

import React, { useRef } from "react"
import ReactCrop, { type Crop } from 'react-image-crop'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Camera, Loader2 } from "lucide-react"

// Types
interface LibraryItem {
  id: string
  content: string
  contentType: 'svg' | 'image'
  title: string
  dateAdded: string
  originalContent?: string
}

// Screenshot Modal Props
interface ScreenshotModalProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  title: string
  onTitleChange: (title: string) => void
  paragraph: string
  onParagraphChange: (paragraph: string) => void
  onGenerate: () => void
  isGenerating: boolean
}

// Color Picker Modal Props
interface ColorPickerModalProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  customColor: string
  onColorChange: (color: string) => void
  onApply: () => void
  presetColors: string[]
}

// Crop Modal Props
interface CropModalProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  cropItem: LibraryItem | null
  crop: Crop | undefined
  onCropChange: (crop: Crop) => void
  onCropComplete: (crop: Crop) => void
  scale: number
  onScaleChange: (scale: number) => void
  rotate: number
  onRotateChange: (rotate: number) => void
  onImageLoad: (e: React.SyntheticEvent<HTMLImageElement>) => void
  onApplyCrop: () => void
  completedCrop: Crop | undefined
  imgRef: React.RefObject<HTMLImageElement | null>
}

// Screenshot Modal Component
export function ScreenshotModal({
  isOpen,
  onOpenChange,
  title,
  onTitleChange,
  paragraph,
  onParagraphChange,
  onGenerate,
  isGenerating
}: ScreenshotModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create a Screenshot</DialogTitle>
          <DialogDescription>Add a title and description to your tier list image.</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <Input
            id="title"
            value={title}
            onChange={(e) => onTitleChange(e.target.value)}
            placeholder="Tier List Title"
          />
          <Textarea
            id="paragraph"
            value={paragraph}
            onChange={(e) => onParagraphChange(e.target.value)}
            placeholder="A witty description..."
          />
        </div>
        <DialogFooter>
          <Button onClick={onGenerate} disabled={isGenerating}>
            {isGenerating ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null} Generate & Download
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Color Picker Modal Component
export function ColorPickerModal({
  isOpen,
  onOpenChange,
  customColor,
  onColorChange,
  onApply,
  presetColors
}: ColorPickerModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Choose Tier Color</DialogTitle>
          <DialogDescription>Select a color for your tier using the color picker or enter a hex value.</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="flex items-center gap-4">
            <label htmlFor="color-picker" className="text-sm font-medium">Color:</label>
            <input
              id="color-picker"
              type="color"
              value={customColor}
              onChange={(e) => onColorChange(e.target.value)}
              className="w-16 h-10 rounded border border-gray-300 cursor-pointer"
            />
            <div className="flex-1">
              <Input
                value={customColor}
                onChange={(e) => onColorChange(e.target.value)}
                placeholder="#000000"
                className="font-mono"
              />
            </div>
          </div>
          <div className="grid grid-cols-8 gap-2">
            {presetColors.map((color) => (
              <button
                key={color}
                className="w-8 h-8 rounded border-2 border-gray-300 hover:border-gray-500 transition-colors"
                style={{ backgroundColor: color }}
                onClick={() => onColorChange(color)}
                title={color}
              />
            ))}
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={onApply}>
            Apply Color
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Crop Modal Component
export function CropModal({
  isOpen,
  onOpenChange,
  cropItem,
  crop,
  onCropChange,
  onCropComplete,
  scale,
  onScaleChange,
  rotate,
  onRotateChange,
  onImageLoad,
  onApplyCrop,
  completedCrop,
  imgRef
}: CropModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Crop Image</DialogTitle>
          <DialogDescription>
            Adjust the crop area and click Apply to save your changes.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {cropItem && (
            <div className="space-y-4">
              <ReactCrop
                crop={crop}
                onChange={(_, percentCrop) => onCropChange(percentCrop)}
                onComplete={(c) => onCropComplete(c)}
                aspect={undefined}
                minWidth={10}
                minHeight={10}
                keepSelection
              >
                <img
                  ref={imgRef}
                  alt="Crop preview"
                  src={cropItem.content}
                  style={{ transform: `scale(${scale}) rotate(${rotate}deg)` }}
                  onLoad={onImageLoad}
                  className="max-h-96 w-auto"
                />
              </ReactCrop>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <label htmlFor="scale" className="text-sm font-medium">Scale:</label>
                  <input
                    id="scale"
                    type="range"
                    min="0.5"
                    max="3"
                    step="0.1"
                    value={scale}
                    onChange={(e) => onScaleChange(Number(e.target.value))}
                    className="w-24"
                  />
                  <span className="text-sm text-muted-foreground">{scale.toFixed(1)}x</span>
                </div>
                <div className="flex items-center gap-2">
                  <label htmlFor="rotate" className="text-sm font-medium">Rotate:</label>
                  <input
                    id="rotate"
                    type="range"
                    min="0"
                    max="360"
                    value={rotate}
                    onChange={(e) => onRotateChange(Number(e.target.value))}
                    className="w-24"
                  />
                  <span className="text-sm text-muted-foreground">{rotate}°</span>
                </div>
              </div>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={onApplyCrop} disabled={!completedCrop}>
            Apply Crop
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
