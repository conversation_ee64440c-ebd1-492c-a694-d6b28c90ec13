"use client"

import React from "react"
import { SortableContext, useSortable } from "@dnd-kit/sortable"
import { useDroppable } from "@dnd-kit/core"
import { CSS } from "@dnd-kit/utilities"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { PlusCircle, Palette, X, Trash2 } from "lucide-react"

// Types
interface LibraryItem {
  id: string
  content: string
  contentType: 'svg' | 'image'
  title: string
  dateAdded: string
  originalContent?: string
}

interface PlacedItem {
  instanceId: string
  libraryId: string
}

interface Tier {
  id: string
  title: string
  color: string
  items: PlacedItem[]
}

interface TierListSectionProps {
  tiers: Tier[]
  libraryItemsById: Record<string, LibraryItem>
  onUpdateTierTitle: (tierId: string, newTitle: string) => void
  onOpenColorPicker: (tierId: string) => void
  onRemoveTier: (tierId: string) => void
  onAddTier: () => void
  tierListRef: React.RefObject<HTMLDivElement | null>
}

// Item Display Component
function ItemDisplay({ item }: { item: LibraryItem }) {
  if (item.contentType === 'svg') {
    return (
      <div 
        className="w-full h-full flex items-center justify-center" 
        dangerouslySetInnerHTML={{ __html: item.content }} 
      />
    )
  } else {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <img 
          src={item.content} 
          alt={item.title} 
          className="w-full h-full object-contain"
        />
      </div>
    )
  }
}

// Tier Item Card Component
function TierItemCard({ item, libraryItem }: { item: PlacedItem; libraryItem: LibraryItem }) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: item.instanceId,
    data: { 
      type: "placed", 
      instanceId: item.instanceId, 
      libraryId: item.libraryId 
    },
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  }

  return (
    <div ref={setNodeRef} style={style} className="flex flex-col items-center gap-1">
      <div
        {...attributes}
        {...listeners}
        className="w-20 h-20 p-1 bg-muted rounded-md cursor-grab active:cursor-grabbing"
      >
        <ItemDisplay item={libraryItem} />
      </div>
      <p className="text-xs text-muted-foreground truncate w-20 text-center">{libraryItem.title}</p>
    </div>
  )
}

// Tier Drop Zone Component
function TierDropZone({ 
  tier, 
  libraryItemsById 
}: { 
  tier: Tier; 
  libraryItemsById: Record<string, LibraryItem> 
}) {
  const { setNodeRef, isOver } = useDroppable({ 
    id: tier.id,
    data: { type: "tier", tierId: tier.id }
  })

  return (
    <Card className="flex-1 rounded-l-none rounded-r-lg bg-gray-800 border-gray-700">
      <CardContent 
        ref={setNodeRef}
        className={`p-2 h-full transition-colors ${isOver ? "bg-gray-700" : ""}`}
      >
        <div className="grid grid-cols-[repeat(auto-fill,minmax(80px,1fr))] gap-x-4 gap-y-6 min-h-[120px]">
          {tier.items.map((item) => {
            const libraryItem = libraryItemsById[item.libraryId]
            if (!libraryItem) return null
            return <TierItemCard key={item.instanceId} item={item} libraryItem={libraryItem} />
          })}
        </div>
      </CardContent>
    </Card>
  )
}

// Trash Can Component
function TrashCan() {
  const { setNodeRef, isOver } = useDroppable({ id: "trash-can" })
  return (
    <div
      ref={setNodeRef}
      className={`mt-8 p-4 border-2 border-dashed rounded-lg flex flex-col items-center justify-center transition-colors ${isOver ? "border-red-500 bg-red-500/10" : "border-muted-foreground"}`}
    >
      <Trash2 className={`h-10 w-10 transition-colors ${isOver ? "text-red-500" : "text-muted-foreground"}`} />
      <p className={`mt-2 font-semibold transition-colors ${isOver ? "text-red-500" : "text-muted-foreground"}`}>
        Drag here to remove
      </p>
    </div>
  )
}

// Main Tier List Section Component
export default function TierListSection({
  tiers,
  libraryItemsById,
  onUpdateTierTitle,
  onOpenColorPicker,
  onRemoveTier,
  onAddTier,
  tierListRef
}: TierListSectionProps) {
  console.log('🏆 Rendering TierListSection with', tiers.length, 'tiers')

  return (
    <>
      <div ref={tierListRef} className="space-y-4 bg-gray-900 p-4 rounded-lg">
        {tiers.map((tier) => (
          <SortableContext key={tier.id} items={tier.items.map((i) => i.instanceId)} id={tier.id}>
            <div className="flex items-stretch min-h-[140px]">
              <div
                className="flex flex-col items-center justify-center w-24 text-white font-bold text-xl rounded-l-lg flex-shrink-0 p-2"
                style={{ backgroundColor: tier.color }}
              >
                <Input
                  value={tier.title}
                  onChange={(e) => onUpdateTierTitle(tier.id, e.target.value)}
                  className="tier-title-input bg-transparent border-0 text-center text-white font-bold text-xl h-auto p-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                />
                <div className="tier-controls flex gap-2 mt-2">
                  <Button
                    size="icon"
                    variant="ghost"
                    className="h-6 w-6 hover:bg-white/20"
                    onClick={() => onOpenColorPicker(tier.id)}
                  >
                    <Palette className="h-4 w-4" />
                  </Button>
                  <Button
                    size="icon"
                    variant="ghost"
                    className="h-6 w-6 hover:bg-white/20"
                    onClick={() => onRemoveTier(tier.id)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <TierDropZone tier={tier} libraryItemsById={libraryItemsById} />
            </div>
          </SortableContext>
        ))}
      </div>

      <div className="flex justify-center">
        <Button onClick={onAddTier}>
          <PlusCircle className="mr-2 h-4 w-4" /> Add Tier
        </Button>
      </div>
      
      <TrashCan />
    </>
  )
}
